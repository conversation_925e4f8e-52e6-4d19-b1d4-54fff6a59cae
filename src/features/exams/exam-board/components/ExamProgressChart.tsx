import { useMemo } from "react";
import { ChartBarIcon } from "@heroicons/react/24/outline";
import type { ExamSchedule } from "../../exams-schedule/types/exams-schedule.type";

interface ExamProgressChartProps {
  schedules: ExamSchedule[];
  isLoading?: boolean;
}

interface MonthlyData {
  month: string;
  completed: number;
  upcoming: number;
  total: number;
}

export const ExamProgressChart = ({
  schedules,
  isLoading,
}: ExamProgressChartProps) => {
  const chartData = useMemo(() => {
    const now = new Date();
    const monthsData: Record<string, MonthlyData> = {};

    // Initialize last 6 months
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthKey = date.toLocaleDateString("en-US", {
        month: "short",
        year: "2-digit",
      });
      monthsData[monthKey] = {
        month: monthKey,
        completed: 0,
        upcoming: 0,
        total: 0,
      };
    }

    // Process schedules
    schedules.forEach((schedule) => {
      const scheduleDate = new Date(schedule.date);
      const monthKey = scheduleDate.toLocaleDateString("en-US", {
        month: "short",
        year: "2-digit",
      });

      if (monthsData[monthKey]) {
        monthsData[monthKey].total++;
        if (scheduleDate < now) {
          monthsData[monthKey].completed++;
        } else {
          monthsData[monthKey].upcoming++;
        }
      }
    });

    return Object.values(monthsData);
  }, [schedules]);

  const maxValue = Math.max(...chartData.map((d) => d.total), 1);

  if (isLoading) {
    return (
      <div className="card bg-base-100 shadow-lg border border-base-300">
        <div className="card-body p-6">
          <div className="skeleton h-6 w-48 mb-4"></div>
          <div className="skeleton h-64 w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-lg border border-base-300">
      <div className="card-body p-6">
        <h3 className="card-title text-lg font-semibold mb-6 flex items-center">
          <ChartBarIcon className="w-5 h-5 mr-2 text-primary" />
          Exam Progress (Last 6 Months)
        </h3>

        <div className="relative">
          {/* Chart */}
          <div className="flex items-end justify-between h-64 mb-4">
            {chartData.map((data, index) => {
              const completedHeight = (data.completed / maxValue) * 100;
              const upcomingHeight = (data.upcoming / maxValue) * 100;

              return (
                <div
                  key={index}
                  className="flex flex-col items-center flex-1 mx-1"
                >
                  <div className="relative w-full max-w-12 h-48 flex flex-col justify-end">
                    {/* Upcoming exams */}
                    {data.upcoming > 0 && (
                      <div
                        className="w-full bg-warning/70 rounded-t transition-all duration-500 ease-out"
                        style={{ height: `${upcomingHeight}%` }}
                        title={`${data.upcoming} upcoming exams`}
                      ></div>
                    )}
                    {/* Completed exams */}
                    {data.completed > 0 && (
                      <div
                        className="w-full bg-success rounded-b transition-all duration-500 ease-out"
                        style={{ height: `${completedHeight}%` }}
                        title={`${data.completed} completed exams`}
                      ></div>
                    )}
                    {/* Total count label */}
                    {data.total > 0 && (
                      <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 text-xs font-medium text-base-content">
                        {data.total}
                      </div>
                    )}
                  </div>
                  {/* Month label */}
                  <div className="text-xs text-base-content/70 mt-2 font-medium">
                    {data.month}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Y-axis labels */}
          <div className="absolute left-0 top-0 h-48 flex flex-col justify-between text-xs text-base-content/60">
            {Array.from({ length: 5 }, (_, i) => (
              <span key={i}>{Math.round((maxValue * (4 - i)) / 4)}</span>
            ))}
          </div>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-center space-x-6 mt-4 text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-success rounded mr-2"></div>
            <span>Completed</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-warning/70 rounded mr-2"></div>
            <span>Upcoming</span>
          </div>
        </div>

        {/* Summary stats */}
        <div className="grid grid-cols-3 gap-4 mt-6 pt-4 border-t border-base-300">
          <div className="text-center">
            <div className="text-lg font-semibold text-success">
              {chartData.reduce((sum, d) => sum + d.completed, 0)}
            </div>
            <div className="text-xs text-base-content/60">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-warning">
              {chartData.reduce((sum, d) => sum + d.upcoming, 0)}
            </div>
            <div className="text-xs text-base-content/60">Upcoming</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-primary">
              {chartData.reduce((sum, d) => sum + d.total, 0)}
            </div>
            <div className="text-xs text-base-content/60">Total</div>
          </div>
        </div>
      </div>
    </div>
  );
};
