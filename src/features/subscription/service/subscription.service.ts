import { logger } from "@/lib/logger";
import type { Subscription } from "../types/subscription.type";
import { sendApiRequest } from "@/common/services/api.service";

type FetchSubscriptionResponse = {
  subscription: Subscription | null;
};

export async function fetchSubscription() {
  try {
    const response = await sendApiRequest<FetchSubscriptionResponse>(
      "/subscriptions/me",
      {
        method: "GET",
        withAuthorization: true,
      }
    );
    return response.subscription;
  } catch (error: unknown) {
    logger.error("Error fetching subscription", error);
    throw error;
  }
}
