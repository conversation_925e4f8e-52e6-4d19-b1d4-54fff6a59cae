export interface Subscription {
  id: string;
  status: "ACTIVE" | "CANCELLED" | "UNPAID";
  paymentCycle: "MONTHLY" | "YEARLY";
  startDate: Date;
  endDate: Date;
  lastPaymentDate: Date;
  cancellationDate: Date | null;
  createdAt: Date;
  plan: {
    id: string;
    description: string | null;
    title: string;
    price: number;
    setupCharges: number;
    branches: number;
    students: number;
    features: string[];
  };
  owner: {
    id: string;
    name: string;
    email: string;
  };
}
