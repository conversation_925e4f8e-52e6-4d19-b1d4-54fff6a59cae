import { FullPageLoader } from "@/common/components/ui/FullPageLoader";
import { useUserProfile } from "@/core/user/user-query";
import { Header } from "@/features/dashboard/components/header/Header";
import { useBranchStore } from "@/features/onboarding/setup/branches/branches.store";
import { useSubscription } from "@/features/subscription/hooks/useSubscription.hook";
import { isBranchAdmin } from "@/utils/user-roles.utils";
import { Navigate, Outlet, useNavigate } from "react-router";

const AppLayout = () => {
  const { data: user, isFetching: isFetchingUserProfile } = useUserProfile();
  const { data: subscription, isFetching: isFetchingSubscription } =
    useSubscription();

  console.log(subscription);
  const { selectedBranch, setSelectedBranch } = useBranchStore();

  if (isFetchingUserProfile || isFetchingSubscription) {
    return (
      <div>
        <FullPageLoader />
      </div>
    );
  }

  if (!subscription || subscription.status !== "ACTIVE") {
    return <Navigate to="/non-active-subscription" />;
  }

  if (!selectedBranch && isBranchAdmin(user)) {
    setSelectedBranch(user.branch);
  }

  return user.isPasswordTemporary ? (
    <Navigate to="/set-password" />
  ) : (
    <div className="flex flex-col min-h-screen pt-[72px]">
      <Header />
      <Outlet />
    </div>
  );
};

export default AppLayout;
