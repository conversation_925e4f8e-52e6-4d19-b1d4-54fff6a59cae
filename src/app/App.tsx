import {
  Route,
  createBrowser<PERSON>outer,
  createRoutesFromElements,
  RouterProvider,
} from "react-router";
import { DashboardLayout } from "@/features/dashboard/DashboardLayout";
import { AuthLayout } from "@/core/auth/layouts/AuthLayout";
import { RequireAuth } from "@/core/auth/components/RequireAuth";
import { PersistentLogin } from "@/core/auth/components/PersistentLogin";
import { Role } from "@/common/constants/roles.constants";
import { SignInPage } from "@/core/auth/pages/SigninPage";
import { UnauthorizedPage } from "@/pages/unAuthorized/UnAuthorizedPage";

// Pages
import { OnboardingPage } from "@/features/onboarding/OnboardingPage";
import { OnboardingLayout } from "@/features/onboarding/layout/OnboardingLayout";
import { DashboardPage } from "@/features/dashboard/dashboardPage";
import { RequireCompleteOnboarding } from "@/features/onboarding/components/RequireCompeleOnBoarding";
import { RequireInCompleteOnboarding } from "@/features/onboarding/components/RequireInCompeleteOnBoarding";
import { NotFoundPage } from "@/pages/NotFoundPage";
import { SubjectsPage } from "@/features/subjects/pages/SubjectsPage";
import { Toaster } from "react-hot-toast";
import { AcademicSessionsPage } from "@/features/academic-session/AcademicSessionsPage";
import AppLayout from "./AppLayout";
import FullScreenErrorComponent from "@/common/components/FullScreenErrorElement";
import { ClassesPage } from "@/features/classes/pages/ClassesPage";
import { StaffPage } from "@/features/staff/pages/StaffPage";
import { AssignSubjectsPage } from "@/features/assign-subjects/pages/AssignSubjectsPage";
import { StudentAdmissionPage } from "@/features/students/pages/StudentAdmissionPage";
import { StudentsListPage } from "@/features/students/pages/StudentsListPage";
import { SetPasswordPage } from "@/core/auth/pages/SetPasswordPage";
import { ExamsPage } from "@/features/exams/create-exams/ExamPage";
import { StudentIdCardsListPage } from "@/features/student-id-cards/pages/StudentIdCardsListPage";
import { CardSetupPage } from "@/features/student-id-cards/pages/CardSetupPage";
import { CardGenerationPage } from "@/features/student-id-cards/pages/CardGenerationPage";
import { StudentAttendancePage } from "@/features/attendance/StudentAttendancePage";
import { AssignExamToClassSectionsPage } from "@/features/exams/class-section-exams/pages/AssignExamToClassSectionsPage";
import { ExamSchedulesPage } from "@/features/exams/exams-schedule/pages/ExamSchedulesPage";
import { ExamResultsPage } from "@/features/exams/exams-results/pages/ExamResultsPage";
import { ExamBoardPage } from "@/features/exams/exam-board/pages/ExamBoardPage";
import { AccountSettingsPage } from "@/features/account-settings/AccountSettingsPage";
import { NonActiveSubscriptionPage } from "@/features/subscription/pages/NonActiveSubscriptionPage";

const router = createBrowserRouter(
  createRoutesFromElements([
    // Public Routes
    <Route element={<AuthLayout />} key="auth">
      <Route path="/login" element={<SignInPage />} />
      <Route path="/set-password" element={<SetPasswordPage />} />
    </Route>,

    <Route path="unauthorized" element={<UnauthorizedPage />} />,
    <Route
      path="/non-active-subscription"
      element={<NonActiveSubscriptionPage />}
    />,

    <Route
      element={<PersistentLogin />}
      errorElement={<FullScreenErrorComponent />}
    >
      <Route element={<RequireAuth allowedRoles={[Role.InstituteOwner]} />}>
        <Route element={<RequireInCompleteOnboarding />}>
          <Route element={<OnboardingLayout />}>
            <Route path="/onboarding" element={<OnboardingPage />} />
          </Route>
        </Route>
      </Route>

      <Route
        element={
          <RequireAuth allowedRoles={[Role.InstituteOwner, Role.BranchAdmin]} />
        }
      >
        <Route element={<AppLayout />}>
          <Route element={<RequireCompleteOnboarding />}>
            <Route element={<DashboardLayout />} key="main">
              <Route path="/" element={<DashboardPage />} />

              <Route path="/staff" element={<StaffPage />} />

              <Route path="/subjects" element={<SubjectsPage />} />

              <Route path="/classes" element={<ClassesPage />} />

              <Route path="/assign-subjects" element={<AssignSubjectsPage />} />

              <Route path="/sessions" element={<AcademicSessionsPage />} />

              <Route path="/admission" element={<StudentAdmissionPage />} />

              <Route
                path="/student-attendance"
                element={<StudentAttendancePage />}
              />

              {/* Student Routes */}
              <Route path="/students" element={<StudentsListPage />} />
              <Route
                path="/students/admission"
                element={<StudentAdmissionPage />}
              />
              <Route
                path="/students/id-cards"
                element={<StudentIdCardsListPage />}
              />
              <Route
                path="/students/id-cards/setup"
                element={<CardSetupPage />}
              />
              <Route
                path="/students/id-cards/generate"
                element={<CardGenerationPage />}
              />

              <Route path="/exams" element={<ExamsPage />} />
              <Route
                path="/exams/assign-to-class-sections"
                element={<AssignExamToClassSectionsPage />}
              />
              <Route path="/exams/schedules" element={<ExamSchedulesPage />} />
              <Route path="/exams/results" element={<ExamResultsPage />} />
              <Route path="/exams/board" element={<ExamBoardPage />} />

              <Route
                path="/account-settings"
                element={<AccountSettingsPage />}
              />
            </Route>
          </Route>
          <Route path="*" element={<NotFoundPage />} />
        </Route>
      </Route>
    </Route>,
  ])
);

const App = () => {
  return (
    <div>
      <Toaster />
      <RouterProvider router={router} />
    </div>
  );
};

export default App;
